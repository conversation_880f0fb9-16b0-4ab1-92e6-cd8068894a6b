pipeline {
    agent any

    parameters {
        choice(name: 'DEPLOY_TYPE', choices: ['snapshot', 'qualif', 'release'], description: 'Deployment Type')
        string(name: 'VERSION', defaultValue: '', description: 'Version to release (required for qualif and release)')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    echo "Starting pipeline with DEPLOY_TYPE: ${DEPLOY_TYPE}"
                    if ((DEPLOY_TYPE == 'qualif' || DEPLOY_TYPE == 'release') && !params.VERSION) {
                        error "VERSION parameter is required for ${DEPLOY_TYPE} deployment"
                    }
                }
            }
        }

        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential'
                sh 'git checkout ${BRANCH_NAME}'
            }
        }

        stage('Configure Git') {
            steps {
                script {
                    sh """
                        git config user.email 'jen<PERSON>@generixgroup.com'
                        git config user.name 'Jenkins'
                    """
                }
            }
        }

        stage('Build') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                sh 'mvn clean package -DskipTests=true'
            }
        }

        stage('Run Tests') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                script {
                    try {
                        sh 'mvn test'
                    } catch (err) {
                        echo "Tests failed, but continuing with pipeline. Error: ${err}"
                    }
                }
            }
        }

        stage('Prepare Version') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            tools {
                maven 'Maven3.6.3'  // Add Maven tool configuration
            }
            steps {
                script {
                    // Extract major.minor from version (e.g., 2.2 from 2.2.1)
                    def versionParts = params.VERSION.split('\\.')
                    def branchVersion = "${versionParts[0]}.${versionParts[1]}"

                    echo "Preparing version ${params.VERSION} on branch qualif/${branchVersion}"

                    withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                        usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                        // Use single quotes to prevent Groovy string interpolation
                        sh '''
                    git remote set-url origin "http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential"

                    if git show-ref --verify --quiet "refs/heads/qualif/''' + branchVersion + '''"; then
                        echo "Branch qualif/''' + branchVersion + ''' exists. Checking out..."
                        git checkout "qualif/''' + branchVersion + '''"
                    else
                        echo "Creating new branch qualif/''' + branchVersion + '''"
                        git checkout -b "qualif/''' + branchVersion + '''"
                    fi

                    # Update version in pom.xml
                    mvn versions:set -DnewVersion=''' + params.VERSION + ''' -DgenerateBackupFiles=false

                    # Check if there are changes to commit
                    if git diff --quiet; then
                        echo "No changes to pom.xml"
                    else
                        git add pom.xml
                        git commit -m "Update version to ''' + params.VERSION + '''"
                        git push origin "qualif/''' + branchVersion + '''"
                    fi
                '''
                    }
                }
            }
        }

        stage('Deploy to Nexus') {
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                script {
                    def nexusBase = 'https://nexus.chassagne-repo.generixgroup.com/content/repositories'
                    def artifactId = 'legal-referential'
                    def groupId = 'com.generix'
                    def packaging = 'jar'

                    if (DEPLOY_TYPE != 'release') {
                        def repositoryUrl = DEPLOY_TYPE == 'snapshot'
                            ? "${nexusBase}/Legal-ref-Snapshots"
                            : "${nexusBase}/legalrefToQualif"

                        withCredentials([usernamePassword(credentialsId: 'nexus-password',
                            usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            // First build the project
                            sh 'mvn clean package -DskipTests=true'

                            sh '''
                                rm -f ../settings.xml
                                cp settings.xml ../settings.xml
                                sed -i s/%USER%/$USERNAME/g ../settings.xml
                                sed -i s/%PASSWORD%/$PASSWORD/g ../settings.xml

                                mvn deploy:deploy-file -s ../settings.xml \
                                    -DgroupId="''' + groupId + '''" \
                                    -DartifactId="''' + artifactId + '''" \
                                    -Dversion="''' + (DEPLOY_TYPE == 'snapshot' ? params.VERSION + '-SNAPSHOT' : params.VERSION) + '''" \
                                    -Dpackaging="''' + packaging + '''" \
                                    -Dfile="target/''' + artifactId + '-' + (DEPLOY_TYPE == 'snapshot' ? params.VERSION + '-SNAPSHOT' : params.VERSION) + '.' + packaging + '''" \
                                    -DrepositoryId="nexus" \
                                    -Durl="''' + repositoryUrl + '''" \
                                    -Dmaven.wagon.http.ssl.insecure=true \
                                    -Dmaven.wagon.http.ssl.allowall=true
                            '''
                        }
                    } else {
                        // Copy from qualif to release repository
                        withCredentials([usernamePassword(credentialsId: 'nexus-password',
                            usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            def qualifUrl = "${nexusBase}/legalrefToQualif/${groupId.replace('.', '/')}/${params.VERSION}/${artifactId}-${params.VERSION}.${packaging}"
                            def releasesUrl = "${nexusBase}/releasesLegal-Ref"

                            sh """
                                mkdir -p target

                                # Download from qualif
                                wget --no-check-certificate --http-user="${USERNAME}" --http-password="${PASSWORD}" \
                                    -O "target/${artifactId}-${params.VERSION}.${packaging}" "${qualifUrl}"

                                # Deploy to releases
                                mvn deploy:deploy-file -s ../settings.xml \
                                    -DgroupId="${groupId}" \
                                    -DartifactId="${artifactId}" \
                                    -Dversion="${params.VERSION}" \
                                    -Dpackaging="${packaging}" \
                                    -Dfile="target/${artifactId}-${params.VERSION}.${packaging}" \
                                    -DrepositoryId="nexus" \
                                    -Durl="${releasesUrl}" \
                                    -Dmaven.wagon.http.ssl.insecure=true \
                                    -Dmaven.wagon.http.ssl.allowall=true
                            """
                        }
                    }
                }
            }
        }

        stage('Create Tag') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            steps {
                withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                    sh """
                        git remote set-url origin 'http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential'
                        git tag -a legel-ref-${params.VERSION} -m 'Version ${params.VERSION}'
                        git push origin legel-ref-${params.VERSION}
                    """
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success {
            echo "Pipeline completed successfully!"
        }
        failure {
            echo "Pipeline failed! Please check the logs for details."
        }
    }
}
