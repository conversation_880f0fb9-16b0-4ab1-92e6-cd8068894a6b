<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.generix.legalreferential</groupId>
    <artifactId>legal-referential</artifactId>
    <version>2.2.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>Legal Referential</name>
    <description>Description for legalReferential</description>

    <repositories>
        <!-- jhipster-needle-maven-repository -->
    </repositories>

    <pluginRepositories>
        <!-- jhipster-needle-maven-plugin-repository -->
    </pluginRepositories>

    <!-- jhipster-needle-distribution-management -->

    <properties>
        <!-- Build properties -->
        <maven.version>3.2.5</maven.version>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <start-class>com.generix.legalreferential.LegalReferentialApp</start-class>
        <argLine>-Djava.security.egd=file:/dev/./urandom -Xmx1G</argLine>
        <m2e.apt.activation>jdt_apt</m2e.apt.activation>
        <run.addResources>false</run.addResources>
        <!-- These remain empty unless the corresponding profile is active -->
        <profile.no-liquibase/>
        <profile.api-docs/>
        <profile.tls/>
        <profile.e2e/>

        <!-- Dependency versions -->
        <jhipster-dependencies.version>7.9.3</jhipster-dependencies.version>
        <!-- The spring-boot version should match the one managed by
        https://mvnrepository.com/artifact/tech.jhipster/jhipster-dependencies/${jhipster-dependencies.version} -->
        <spring-boot.version>2.7.3</spring-boot.version>
        <!-- The hibernate version should match the one managed by
        https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-dependencies/${spring-boot.version} -->
        <hibernate.version>5.6.10.Final</hibernate.version>
        <!-- The liquibase version should match the one managed by
        https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-dependencies/${spring-boot.version} -->
        <liquibase.version>4.15.0</liquibase.version>
        <liquibase-hibernate5.version>4.15.0</liquibase-hibernate5.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <jaxb-runtime.version>4.0.0</jaxb-runtime.version>
        <archunit-junit5.version>0.22.0</archunit-junit5.version>
        <mapstruct.version>1.5.2.Final</mapstruct.version>
        <jackson-databind-nullable.version>0.2.3</jackson-databind-nullable.version>
        <caffeine.version>3.1.1</caffeine.version>
        <!-- Plugin versions -->
        <maven-clean-plugin.version>3.2.0</maven-clean-plugin.version>
        <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <maven-javadoc-plugin.version>3.4.1</maven-javadoc-plugin.version>
        <maven-eclipse-plugin.version>2.10</maven-eclipse-plugin.version>
        <maven-enforcer-plugin.version>3.1.0</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>3.0.0-M7</maven-failsafe-plugin.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-idea-plugin.version>2.2.1</maven-idea-plugin.version>
        <maven-resources-plugin.version>3.3.0</maven-resources-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.3.2</maven-war-plugin.version>
        <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
        <checkstyle.version>10.3.2</checkstyle.version>
        <nohttp-checkstyle.version>0.0.10</nohttp-checkstyle.version>
        <git-commit-id-plugin.version>5.0.0</git-commit-id-plugin.version>
        <modernizer-maven-plugin.version>2.4.0</modernizer-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.8</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>3.2.1</jib-maven-plugin.version>
        <jib-maven-plugin.image>eclipse-temurin:11-jre-focal</jib-maven-plugin.image>
        <jib-maven-plugin.architecture>amd64</jib-maven-plugin.architecture>
        <lifecycle-mapping.version>1.0.0</lifecycle-mapping.version>
        <openapi-generator-maven-plugin.version>6.0.1</openapi-generator-maven-plugin.version>
        <properties-maven-plugin.version>1.1.0</properties-maven-plugin.version>
        <sonar-maven-plugin.version>3.9.0</sonar-maven-plugin.version>

        <!-- jhipster-needle-maven-property -->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>tech.jhipster</groupId>
                <artifactId>jhipster-dependencies</artifactId>
                <version>${jhipster-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- jhipster-needle-maven-add-dependency-management -->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>jaxrs-api</artifactId>
            <version>3.0.12.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.5.1</version> <!-- or the latest version -->
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.generix.tokenvalidator</groupId>
            <artifactId>tokenvalidator</artifactId>
            <version>0.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>
        <dependency>
            <groupId>tech.jhipster</groupId>
            <artifactId>jhipster-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate5</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hppc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-webmvc-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jcache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jpamodelgen</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <!-- Inherited version from Spring Boot can't be used because of regressions -->
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-loader-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>jdbc</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5-api</artifactId>
            <version>${archunit-junit5.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- Adding the engine dependency to the surefire-plugin unfortunately does not work in the current version. -->
        <!-- https://www.archunit.org/userguide/html/000_Index.html#_junit_5 -->
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5-engine</artifactId>
            <version>${archunit-junit5.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!-- Spring Security OAuth 2.0 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <!-- jwt Security-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- Spring Cloud -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-data</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
        </dependency>
        <!-- Cucumber -->
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit-platform-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-console</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- jhipster-needle-maven-add-dependency -->

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hierynomus</groupId>
            <artifactId>sshj</artifactId>
            <version>0.38.0</version>
        </dependency>

    </dependencies>

    <build>
        <defaultGoal>spring-boot:run</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-idea-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.gaul</groupId>
                <artifactId>modernizer-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <!--
                    Plugin that provides API-first development using openapi-generator-cli to
                    generate Spring-MVC endpoint stubs at compile time from an OpenAPI definition file
                -->
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
            </plugin>
            <!-- jhipster-needle-maven-add-plugin -->
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>io.spring.nohttp</groupId>
                            <artifactId>nohttp-checkstyle</artifactId>
                            <version>${nohttp-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>checkstyle.xml</configLocation>
                        <includes>pom.xml,README.md</includes>
                        <excludes>.git/**/*,target/**/*,node_modules/**/*,node/**/*</excludes>
                        <sourceDirectories>./</sourceDirectories>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring-boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <!-- For JPA static metamodel generation -->
                            <path>
                                <groupId>org.hibernate</groupId>
                                <artifactId>hibernate-jpamodelgen</artifactId>
                                <version>${hibernate.version}</version>
                            </path>
                            <path>
                                <groupId>org.glassfish.jaxb</groupId>
                                <artifactId>jaxb-runtime</artifactId>
                                <version>${jaxb-runtime.version}</version>
                            </path>
                            <!-- jhipster-needle-maven-add-annotation-processor -->
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven-war-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default-war</id>
                            <goals>
                                <goal>war</goal>
                            </goals>
                            <phase>package</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <warSourceIncludes>WEB-INF/**,META-INF/**</warSourceIncludes>
                        <failOnMissingWebXml>false</failOnMissingWebXml>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>${properties-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>initialize</phase>
                            <goals>
                                <goal>read-project-properties</goal>
                            </goals>
                            <configuration>
                                <files>
                                    <file>sonar-project.properties</file>
                                </files>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <failOnNoGitDirectory>false</failOnNoGitDirectory>
                        <failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <includeOnlyProperties>
                            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.describe$</includeOnlyProperty>
                            <includeOnlyProperty>^git.branch$</includeOnlyProperty>
                        </includeOnlyProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.gaul</groupId>
                    <artifactId>modernizer-maven-plugin</artifactId>
                    <version>${modernizer-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>modernizer</id>
                            <phase>package</phase>
                            <goals>
                                <goal>modernizer</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <javaVersion>${java.version}</javaVersion>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>pre-unit-tests</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <!-- Ensures that the code coverage report for unit tests is created after unit tests have been run -->
                        <execution>
                            <id>post-unit-test</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>pre-integration-tests</id>
                            <goals>
                                <goal>prepare-agent-integration</goal>
                            </goals>
                        </execution>
                        <!-- Ensures that the code coverage report for integration tests is created after integration tests have been run -->
                        <execution>
                            <id>post-integration-tests</id>
                            <phase>post-integration-test</phase>
                            <goals>
                                <goal>report-integration</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>${jib-maven-plugin.version}</version>
                    <configuration>
                        <from>
                            <image>${jib-maven-plugin.image}</image>
                            <auth>
                                <username>chassagne</username>
                                <password>CH4SS4GN3!</password>
                            </auth>
                            <platforms>
                                <platform>
                                    <architecture>${jib-maven-plugin.architecture}</architecture>
                                    <os>linux</os>
                                </platform>
                            </platforms>
                        </from>
                        <to>
                            <image>chassagne/legal-referential:latest</image>
                            <!--<image>legalreferential:latest</image>-->
                            <auth>
                                <username>chassagne</username>
                                <password>CH4SS4GN3!</password>
                            </auth>
                        </to>
                        <container>
                            <entrypoint>
                                <shell>bash</shell>
                                <option>-c</option>
                                <arg>/entrypoint.sh</arg>
                            </entrypoint>
                            <ports>
                                <port>8081</port>
                            </ports>
                            <environment>
                                <SPRING_OUTPUT_ANSI_ENABLED>ALWAYS</SPRING_OUTPUT_ANSI_ENABLED>
                                <JHIPSTER_SLEEP>0</JHIPSTER_SLEEP>
                            </environment>
                            <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                            <user>1000</user>
                        </container>
                        <extraDirectories>
                            <paths>src/main/docker/jib</paths>
                            <permissions>
                                <permission>
                                    <file>/entrypoint.sh</file>
                                    <mode>755</mode>
                                </permission>
                            </permissions>
                        </extraDirectories>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>${maven-eclipse-plugin.version}</version>
                    <configuration>
                        <downloadSources>true</downloadSources>
                        <downloadJavadocs>true</downloadJavadocs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>enforce-versions</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>enforce-dependencyConvergence</id>
                            <configuration>
                                <rules>
                                    <DependencyConvergence/>
                                </rules>
                                <fail>false</fail>
                            </configuration>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <rules>
                            <requireMavenVersion>
                                <message>You are running an older version of Maven. JHipster requires at least Maven
                                    ${maven.version}
                                </message>
                                <version>[${maven.version},)</version>
                            </requireMavenVersion>
                            <requireJavaVersion>
                                <message>You are running an incompatible version of Java. JHipster supports JDK 11
                                    to 18.
                                </message>
                                <version>[11,12),[12,13),[13,14),[14,15),[15,16),[16,17),[17,18),[18,19)</version>
                            </requireJavaVersion>
                        </rules>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-idea-plugin</artifactId>
                    <version>${maven-idea-plugin.version}</version>
                    <configuration>
                        <exclude>node_modules</exclude>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default-resources</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.build.directory}/classes</outputDirectory>
                                <useDefaultDelimiters>false</useDefaultDelimiters>
                                <delimiters>
                                    <delimiter>#</delimiter>
                                </delimiters>
                                <resources>
                                    <resource>
                                        <directory>src/main/resources/</directory>
                                        <filtering>true</filtering>
                                        <includes>
                                            <include>config/*.yml</include>
                                        </includes>
                                    </resource>
                                    <resource>
                                        <directory>src/main/resources/</directory>
                                        <filtering>false</filtering>
                                        <excludes>
                                            <exclude>config/application*.yml</exclude>
                                        </excludes>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <!-- Force alphabetical order to have a reproducible build -->
                        <runOrder>alphabetical</runOrder>
                        <excludes>
                            <exclude>**/*IT*</exclude>
                            <exclude>**/*IntTest*</exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <!--
                        Plugin that provides API-first development using openapi-generator-cli to
                        generate Spring-MVC endpoint stubs at compile time from an OpenAPI definition file
                    -->
                    <groupId>org.openapitools</groupId>
                    <artifactId>openapi-generator-maven-plugin</artifactId>
                    <version>${openapi-generator-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>generate</goal>
                            </goals>
                            <configuration>
                                <inputSpec>${project.basedir}/src/main/resources/swagger/api.yml</inputSpec>
                                <generatorName>spring</generatorName>
                                <apiPackage>com.generix.legalreferential.web.api</apiPackage>
                                <modelPackage>com.generix.legalreferential.service.api.dto</modelPackage>
                                <supportingFilesToGenerate>ApiUtil.java</supportingFilesToGenerate>
                                <importMappings>Problem=org.zalando.problem.Problem</importMappings>
                                <skipValidateSpec>false</skipValidateSpec>
                                <configOptions>
                                    <delegatePattern>true</delegatePattern>
                                    <title>legal-referential</title>
                                </configOptions>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <mainClass>${start-class}</mainClass>
                        <fork>true</fork>
                        <!--
                        Enable the line below to have remote debugging of your application on port 5005
                        <jvmArguments>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005</jvmArguments>
                        -->
                    </configuration>

                </plugin>
                <!-- jhipster-needle-maven-add-plugin-management -->
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <profile>
            <id>spanshot</id>
            <distributionManagement>
                <repository>
                    <id>snapshotsForLegalref</id>
                    <url>
                        https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots
                    </url>
                </repository>
            </distributionManagement>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <optional>true</optional>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.testcontainers</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <excludes>
                                <exclude>config/application-*.yml</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>3.3.0</version>
                        <executions>
                            <execution>
                                <id>create-zip</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <descriptors>
                                        <descriptor>assembly.xml</descriptor>
                                    </descriptors>
                                    <finalName>${project.artifactId}-${project.version}</finalName>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <distributionManagement>
                <repository>
                    <id>releasesLegalRef</id>
                    <url>
                        https://nexus.chassagne-repo.generixgroup.com/content/repositories/releasesLegal-Ref
                    </url>
                </repository>
                <snapshotRepository>
                    <id>snapshotsForLegalref</id>
                    <url>
                        https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots
                    </url>
                </snapshotRepository>
            </distributionManagement>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <optional>true</optional>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.testcontainers</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <excludes>
                                <exclude>config/application-*.yml</exclude>
                            </excludes>
                        </configuration>
                    </plugin>


                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>3.3.0</version>
                        <executions>
                            <execution>
                                <id>create-zip</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <descriptors>
                                        <descriptor>assembly.xml</descriptor>
                                    </descriptors>
                                    <finalName>${project.artifactId}-${project.version}</finalName>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>qualif</id>
            <distributionManagement>
                <repository>
                    <id>legalRefToQualify</id>
                    <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/legalrefToQualif
                    </url>
                </repository>
                <snapshotRepository>
                    <id>snapshotsForLegalref</id>
                    <url>
                        https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots
                    </url>
                </snapshotRepository>
            </distributionManagement>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <optional>true</optional>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.testcontainers</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <excludes>
                                <exclude>config/application-*.yml</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>3.3.0</version>
                        <executions>
                            <execution>
                                <id>create-zip</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <descriptors>
                                        <descriptor>assembly.xml</descriptor>
                                    </descriptors>
                                    <finalName>${project.artifactId}-${project.version}</finalName>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>no-liquibase</id>
            <properties>
                <profile.no-liquibase>,no-liquibase</profile.no-liquibase>
            </properties>
        </profile>
        <profile>
            <id>api-docs</id>
            <properties>
                <profile.api-docs>,api-docs</profile.api-docs>
            </properties>
        </profile>
        <profile>
            <id>tls</id>
            <properties>
                <profile.tls>,tls</profile.tls>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <optional>true</optional>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>

                    <artifactId>postgresql</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.testcontainers</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <excludes>
                                <exclude>config/application-*.yml</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>3.3.0</version>
                        <executions>
                            <execution>
                                <id>create-zip</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <descriptors>
                                        <descriptor>assembly.xml</descriptor>
                                    </descriptors>
                                    <finalName>${project.artifactId}-${project.version}</finalName>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <version>${maven-failsafe-plugin.version}</version>
                            <configuration>
                                <!-- Due to spring-boot repackage, without adding this property test classes are not found
                                     See https://github.com/spring-projects/spring-boot/issues/6254 -->
                                <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                                <!-- Force alphabetical order to have a reproducible build -->
                                <runOrder>alphabetical</runOrder>
                                <includes>
                                    <include>**/*IT*</include>
                                    <include>**/*IntTest*</include>
                                </includes>
                                <argLine>@{argLine} -Dspring.profiles.active=testdev</argLine>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>integration-test</id>
                                    <goals>
                                        <goal>integration-test</goal>
                                    </goals>
                                </execution>
                                <execution>
                                    <id>verify</id>
                                    <goals>
                                        <goal>verify</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>org.liquibase</groupId>
                            <artifactId>liquibase-maven-plugin</artifactId>
                            <version>${liquibase.version}</version>
                            <configuration>
                                <changeLogFile>${project.basedir}/src/main/resources/config/liquibase/master.xml
                                </changeLogFile>
                                <diffChangeLogFile>
                                    ${project.basedir}/src/main/resources/config/liquibase/changelog/${maven.build.timestamp}_changelog.xml
                                </diffChangeLogFile>
                                <driver>org.postgresql.Driver</driver>
                                <url>*************************************************</url>
                                <defaultSchemaName></defaultSchemaName>
                                <username>legalReferential</username>
                                <password></password>
                                <referenceUrl>
                                    hibernate:spring:com.generix.legalreferential.domain?dialect=tech.jhipster.domain.util.FixedPostgreSQL10Dialect&amp;hibernate.physical_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy&amp;hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
                                </referenceUrl>
                                <verbose>true</verbose>
                                <logging>debug</logging>
                                <contexts>!test</contexts>
                                <diffExcludeObjects>oauth_access_token, oauth_approvals, oauth_client_details,
                                    oauth_client_token, oauth_code, oauth_refresh_token
                                </diffExcludeObjects>
                            </configuration>
                            <dependencies>
                                <dependency>
                                    <groupId>org.liquibase</groupId>
                                    <artifactId>liquibase-core</artifactId>
                                    <version>${liquibase.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.liquibase.ext</groupId>
                                    <artifactId>liquibase-hibernate5</artifactId>
                                    <version>${liquibase-hibernate5.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.springframework.boot</groupId>
                                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                                    <version>${spring-boot.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>javax.validation</groupId>
                                    <artifactId>validation-api</artifactId>
                                    <version>${validation-api.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>tech.jhipster</groupId>
                                    <artifactId>jhipster-framework</artifactId>
                                    <version>${jhipster-dependencies.version}</version>
                                </dependency>
                            </dependencies>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
            <properties>
                <!-- default Spring profiles -->
                <spring.profiles.active>dev${profile.tls}${profile.no-liquibase}</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <dependencies>
                <dependency>
                    <groupId>org.testcontainers</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </dependency>
            </dependencies>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <version>${maven-failsafe-plugin.version}</version>
                            <configuration>
                                <!-- Due to spring-boot repackage, without adding this property test classes are not found
                                     See https://github.com/spring-projects/spring-boot/issues/6254 -->
                                <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                                <!-- Force alphabetical order to have a reproducible build -->
                                <runOrder>alphabetical</runOrder>
                                <includes>
                                    <include>**/*IT*</include>
                                    <include>**/*IntTest*</include>
                                </includes>
                                <argLine>@{argLine} -Dspring.profiles.active=testprod</argLine>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>integration-test</id>
                                    <goals>
                                        <goal>integration-test</goal>
                                    </goals>
                                </execution>
                                <execution>
                                    <id>verify</id>
                                    <goals>
                                        <goal>verify</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>org.liquibase</groupId>
                            <artifactId>liquibase-maven-plugin</artifactId>
                            <version>${liquibase.version}</version>
                            <configuration>
                                <changeLogFile>${project.basedir}/src/main/resources/config/liquibase/master.xml
                                </changeLogFile>
                                <diffChangeLogFile>
                                    ${project.basedir}/src/main/resources/config/liquibase/changelog/${maven.build.timestamp}_changelog.xml
                                </diffChangeLogFile>
                                <driver>org.postgresql.Driver</driver>
                                <url>*************************************************</url>
                                <defaultSchemaName></defaultSchemaName>
                                <username>legalReferential</username>
                                <password></password>
                                <referenceUrl>
                                    hibernate:spring:com.generix.legalreferential.domain?dialect=tech.jhipster.domain.util.FixedPostgreSQL10Dialect&amp;hibernate.physical_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy&amp;hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
                                </referenceUrl>
                                <verbose>true</verbose>
                                <logging>debug</logging>
                                <contexts>!test</contexts>
                                <diffExcludeObjects>oauth_access_token, oauth_approvals, oauth_client_details,
                                    oauth_client_token, oauth_code, oauth_refresh_token
                                </diffExcludeObjects>
                            </configuration>
                            <dependencies>
                                <dependency>
                                    <groupId>org.liquibase</groupId>
                                    <artifactId>liquibase-core</artifactId>
                                    <version>${liquibase.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.liquibase.ext</groupId>
                                    <artifactId>liquibase-hibernate5</artifactId>
                                    <version>${liquibase-hibernate5.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.springframework.boot</groupId>
                                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                                    <version>${spring-boot.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>javax.validation</groupId>
                                    <artifactId>validation-api</artifactId>
                                    <version>${validation-api.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>tech.jhipster</groupId>
                                    <artifactId>jhipster-framework</artifactId>
                                    <version>${jhipster-dependencies.version}</version>
                                </dependency>
                            </dependencies>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <plugin>
                        <artifactId>maven-clean-plugin</artifactId>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>target/classes/static/</directory>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>build-info</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
            <properties>
                <!-- default Spring profiles -->
                <spring.profiles.active>
                    prod${profile.api-docs}${profile.tls}${profile.e2e}${profile.no-liquibase}
                </spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>war</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!--
                Profile for tracing requests with Zipkin.
            -->
            <id>zipkin</id>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-sleuth</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-sleuth-zipkin</artifactId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <!--
                Profile for applying IDE-specific configuration.
                At the moment it configures MapStruct and Hibernate JPA Metamodel Generator, which you need when working
                with DTOs and entity filtering.
            -->
            <id>IDE</id>
            <dependencies>
                <dependency>
                    <groupId>org.mapstruct</groupId>
                    <artifactId>mapstruct-processor</artifactId>
                    <version>${mapstruct.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-jpamodelgen</artifactId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <!-- This is automatically activated when working in Eclipse -->
            <id>eclipse</id>
            <activation>
                <property>
                    <name>m2e.version</name>
                </property>
            </activation>
            <dependencies>
                <!-- The following dependency is added due to issue #9175-->
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-undertow</artifactId>
                </dependency>
            </dependencies>
            <build>
                <pluginManagement>
                    <plugins>
                        <!--
                            This plugin's configuration is used to store Eclipse m2e settings only.
                            It has no influence on the Maven build itself.
                            Remove when the m2e plugin can correctly bind to Maven lifecycle
                        -->
                        <plugin>
                            <groupId>org.eclipse.m2e</groupId>
                            <artifactId>lifecycle-mapping</artifactId>
                            <version>${lifecycle-mapping.version}</version>
                            <configuration>
                                <lifecycleMappingMetadata>
                                    <pluginExecutions>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>org.jacoco</groupId>
                                                <artifactId>
                                                    jacoco-maven-plugin
                                                </artifactId>
                                                <versionRange>
                                                    ${jacoco-maven-plugin.version}
                                                </versionRange>
                                                <goals>
                                                    <goal>prepare-agent</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore/>
                                            </action>
                                        </pluginExecution>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>org.openapitools</groupId>
                                                <artifactId>openapi-generator-maven-plugin</artifactId>
                                                <versionRange>${openapi-generator-maven-plugin.version}</versionRange>
                                                <goals>
                                                    <goal>generate</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore/>
                                            </action>
                                        </pluginExecution>
                                    </pluginExecutions>
                                </lifecycleMappingMetadata>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <!-- jhipster-needle-maven-add-profile -->
    </profiles>
</project>
